import os
import jwt
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
import requests
from functools import lru_cache
from lib.supabase import supabase

# JWT configuration - loaded lazily to support testing
def get_supabase_url():
    url = os.getenv("SUPABASE_URL")
    if not url:
        raise ValueError("SUPABASE_URL environment variable is required")
    return url

# Security scheme for Bearer token
security = HTTPBearer()

@lru_cache()
def get_supabase_jwt_secret():
    """Get JWT secret from environment or fetch from Supabase"""
    jwt_secret = os.getenv("SUPABASE_JWT_SECRET")
    if jwt_secret:
        return jwt_secret

    # If not in env, try to fetch from Supabase API
    try:
        supabase_url = get_supabase_url()
        response = requests.get(f"{supabase_url}/rest/v1/")
        # This is a fallback - in production, JWT secret should be in environment
        raise ValueError("SUPABASE_JWT_SECRET environment variable is required")
    except Exception:
        raise ValueError("SUPABASE_JWT_SECRET environment variable is required")

def verify_jwt_token_with_supabase(token: str) -> dict:
    """Verify JWT token using Supabase client"""
    try:
        # First, let's decode the token without verification to see what's in it
        print(f"Attempting to verify token: {token[:50]}...")

        # Decode without verification to inspect the token
        unverified_payload = jwt.decode(token, options={"verify_signature": False})
        print(f"Token payload (unverified): {unverified_payload}")

        # Use Supabase client to verify the token
        response = supabase.auth.get_user(token)

        if response.user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Return user information
        return {
            "id": response.user.id,
            "email": response.user.email,
            "role": "authenticated",
            "aud": "authenticated"
        }

    except Exception as e:
        print(f"Token verification error: {str(e)}")
        print(f"Error type: {type(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )

def verify_jwt_token(token: str) -> dict:
    """Verify and decode JWT token from Supabase using the new getClaims method"""
    try:
        # Use Supabase client's getClaims method for token verification
        # This is the recommended approach for the new API keys
        claims = supabase.auth.get_claims(token)

        if not claims:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Validate that we have the required fields
        if not claims.get("sub"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token: missing user ID",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Return the claims with user information
        return claims

    except HTTPException:
        # Re-raise HTTP exceptions (authentication failures)
        raise
    except Exception as e:
        print(f"Token verification error: {str(e)}")
        # SECURITY: Do not fall back to unverified tokens!
        # Any authentication failure should result in 401
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> dict:
    """
    Dependency to get current authenticated user from JWT token
    Returns the user payload from the JWT token
    """
    token = credentials.credentials
    user_payload = verify_jwt_token(token)

    # Extract user information from the payload
    user_id = user_payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: missing user ID",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return {
        "id": user_id,
        "email": user_payload.get("email"),
        "role": user_payload.get("role", "authenticated"),
        "aud": user_payload.get("aud"),
        "exp": user_payload.get("exp"),
        "iat": user_payload.get("iat"),
    }

async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[dict]:
    """
    Optional dependency to get current user - returns None if no token provided
    Useful for endpoints that can work with or without authentication
    """
    if not credentials:
        return None

    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None
