from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import base64
import uuid
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import authentication dependencies
from auth.dependencies import get_current_user

app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development only, restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class ProcessImageRequest(BaseModel):
    imageDataUrl: str
    style: str
    customizations: Optional[str] = None

class ProcessImageResponse(BaseModel):
    redesignedImageUrl: str

@app.get("/")
async def root():
    return {"message": "Hello World from Web Interior Designer API!"}

@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "service": "web-interior-designer-api"}

# Add a simple version endpoint
@app.get("/api/version")
async def version():
    return {"version": "0.1.0", "name": "Web Interior Designer API - Hello World"}

@app.post("/process-image", response_model=ProcessImageResponse)
async def process_image(
    request: ProcessImageRequest,
    current_user: dict = Depends(get_current_user)
) -> ProcessImageResponse:
    """
    Protected endpoint that processes interior design images.
    Requires authentication via JWT token.
    """
    try:
        # Log the authenticated user (for debugging)
        print(f"Processing image for user: {current_user['id']} ({current_user.get('email', 'no email')})")

        # Validate the image data URL
        if not request.imageDataUrl.startswith('data:image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid image data URL format"
            )

        # For now, we'll implement a simple mock response that returns the original image
        # In a real implementation, this would:
        # 1. Decode the base64 image
        # 2. Call OpenAI API for image processing
        # 3. Store the result in cloud storage
        # 4. Return the URL to the processed image

        # Mock implementation - return the original image as the "redesigned" version
        # This allows us to test the authentication flow
        redesigned_url = request.imageDataUrl

        # In dry mode or for testing, we can add some processing simulation
        print(f"Style requested: {request.style}")
        if request.customizations:
            print(f"Customizations: {request.customizations}")

        return ProcessImageResponse(redesignedImageUrl=redesigned_url)

    except HTTPException:
        # Re-raise HTTP exceptions (like authentication errors)
        raise
    except Exception as e:
        # Handle any other errors
        print(f"Error processing image: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process image: {str(e)}"
        )

@app.post("/api/jobs", response_model=ProcessImageResponse)
async def create_job(
    request: ProcessImageRequest,
    current_user: dict = Depends(get_current_user)
) -> ProcessImageResponse:
    """
    Alternative endpoint name for compatibility with planning documents.
    This is the same as /process-image but follows the /api/jobs pattern.
    """
    return await process_image(request, current_user)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
